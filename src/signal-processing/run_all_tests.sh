#!/bin/bash
set -e

echo "Signal Processing Module - Complete Test Suite"
echo "=============================================="

SRC_DIR=$(cd "$(dirname "$0")" && pwd)
cd "$SRC_DIR"

echo ""
echo "Testing Original FindFrontFrwd Algorithm..."
echo "-------------------------------------------"
cd find-front-frwd
./run_tests.sh
cd ..

echo ""
echo "Testing Simplified FindFrontFrwdNoAve Algorithm..."
echo "--------------------------------------------------"
cd find-front-frwd-no-ave
./run_tests.sh
cd ..

echo ""
echo "Testing Value-Based SyncByFrwd Algorithm..."
echo "-------------------------------------------"
cd sync-by-frwd
./run_tests.sh
cd ..

echo ""
echo "Testing Functional Equivalence..."
echo "---------------------------------"
if [ -f test_equivalence.cpp ]; then
    if [ ! -f test_equivalence ]; then
        echo "Building equivalence test..."
        CXX=${CXX:-g++}
        CXXFLAGS="-std=c++17 -Wall -Wextra -O2 -g -pedantic"
        INCLUDES="-I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline"

        $CXX $CXXFLAGS $INCLUDES \
            find-front-frwd/find_front_frwd.cpp \
            find-front-frwd-no-ave/find_front_frwd_no_ave.cpp \
            test_equivalence.cpp \
            -o test_equivalence
    fi
    ./test_equivalence
else
    echo "Equivalence test not found - skipping"
fi

echo ""
echo "🎉 ALL SIGNAL PROCESSING TESTS COMPLETED SUCCESSFULLY!"
echo "======================================================="
echo "✓ Original algorithm: All tests passed"
echo "✓ Simplified algorithm: All tests passed"
echo "✓ Value-based synchronization: All tests passed"
echo "✓ Functional equivalence: Verified"
echo ""
echo "All three signal processing algorithms are ready for use:"
echo "• FindFrontFrwd: Full-featured with averaging support"
echo "• FindFrontFrwdNoAve: Simplified without averaging"
echo "• SyncByFrwd: Value-based synchronization with interpolation"
