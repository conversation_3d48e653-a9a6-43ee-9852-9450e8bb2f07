#!/bin/bash
set -e

CXX=${CXX:-g++}
CXXFLAGS="-std=c++17 -Wall -Wextra -O2 -g -pedantic"
INCLUDES="-I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline"

SRC_DIR=$(cd "$(dirname "$0")" && pwd)
cd "$SRC_DIR"

echo "SyncByFrwd Test Runner"
echo "======================"

$CXX $CXXFLAGS $INCLUDES sync_by_frwd.cpp tests/test_sync_by_frwd.cpp -o sync_by_frwd_tests
$CXX $CXXFLAGS $INCLUDES sync_by_frwd.cpp tests/test_sync_detection.cpp -o sync_detection_tests
$CXX $CXXFLAGS $INCLUDES sync_by_frwd.cpp tests/test_sync_edge_cases.cpp -o sync_edge_cases_tests
$CXX $CXXFLAGS $INCLUDES sync_by_frwd.cpp tests/test_bug_regression.cpp -o bug_regression_tests

echo "Running general algorithm tests..."
./sync_by_frwd_tests

echo ""
echo "Running synchronization detection tests..."
./sync_detection_tests

echo ""
echo "Running edge cases tests..."
./sync_edge_cases_tests

echo ""
echo "Running bug regression tests..."
./bug_regression_tests

echo "All SyncByFrwd tests completed."
