#pragma once

#include <vector>
#include "../../types.h"

namespace IQVideoProcessor::SignalProcessing {

/**
 * SyncByFrwd - Value-based synchronization algorithm
 *
 * This algorithm extends the simplified front-finding approach with value-based
 * synchronization. It detects fronts and then verifies that the threshold window
 * crosses a specific searched value, providing precise interpolated positioning.
 *
 * Key features:
 * - Front detection with delta threshold validation
 * - Value crossing verification within threshold window
 * - Linear interpolation for precise value location
 * - Support for rising, falling, and bidirectional fronts
 * - No averaging (always behaves as if aveSize=1)
 * - No ring buffer dependency
 */
template <typename TFloat = float>
class SyncByFrwd {
public:
  // Constructor: only needs data pointer and effective length
  explicit SyncByFrwd(const TFloat* data, std::size_t effectiveLen);

  // Value-based synchronization operator with searched value parameter
  // Returns true on successful execution (not necessarily detection).
  bool operator()(bool toEOF,
                  int frontType,
                  TFloat dblSearchedValue,
                  TFloat dblFromPosition,
                  TFloat dblElements,
                  TFloat dblThresholdSize,
                  TFloat dblThresholdTrigDelta,
                  TFloat& frontPos,
                  bool& frontFound) const;

private:
  const TFloat* data_{nullptr};
  std::size_t effectiveLen_{0};

  TFloat findPreciseValueLocation(const TFloat* data, size_t dataSize, TFloat searchedValue, int frontType) const;
  inline TFloat interpolatePosition(TFloat val1, TFloat val2, TFloat searchedValue) const;
};

} // namespace IQVideoProcessor::SignalProcessing
