#include "sync_by_frwd.h"
#include <cassert>
#include <cmath>

namespace IQVideoProcessor::SignalProcessing {

using std::abs;
using std::ceil;

template <typename TFloat>
SyncByFrwd<TFloat>::SyncByFrwd(const TFloat* data, std::size_t effectiveLen)
  : data_(data), effectiveLen_(effectiveLen) {
  // Allow empty buffers: data_ may be null when effectiveLen_ == 0
  assert((effectiveLen_ == 0) || (data_ != nullptr));
}

template <typename TFloat>
bool SyncByFrwd<TFloat>::operator()(
  bool toEOF,
  int frontType,
  TFloat dblSearchedValue,
  TFloat dblFromPosition,
  TFloat dblElements,
  TFloat dblThresholdSize,
  TFloat dblThresholdTrigDelta,
  TFloat& frontPos,
  bool& frontFound) const
{
  frontFound = false;

  // Simplified parameter derivation - no averaging logic needed
  // aveSize is always 1, so no odd forcing or halfAveSize calculation
  const size_t thresholdSize = dblThresholdSize <= static_cast<TFloat>(1) ? 1 : static_cast<size_t>(dblThresholdSize + static_cast<TFloat>(0.5));

  TFloat dblEndPosition = dblFromPosition + dblElements;
  const size_t processingStartPos = static_cast<size_t>(dblFromPosition);

  size_t processingSize;
  if (toEOF) {
    processingSize = (processingStartPos < effectiveLen_) ? (effectiveLen_ - processingStartPos) : 0u;
    dblEndPosition = static_cast<TFloat>(effectiveLen_ - 1); // to EOF means to the last element
  } else if (dblElements <= static_cast<TFloat>(0)) {
    processingSize = 0u;
  } else {
    processingSize = static_cast<size_t>(ceil(dblEndPosition)) - processingStartPos;
    if (processingStartPos + processingSize > effectiveLen_) processingSize = effectiveLen_ - processingStartPos;
  }
  if (processingSize == 0 || processingSize <= thresholdSize) return true; // nothing to process, but not an error

  const TFloat* const data = data_ + processingStartPos; // base pointer for processing

  // No ring buffer initialization needed - we read directly from data
  
  // Threshold is used as-is (no scaling for averaging since aveSize=1)
  TFloat searchedDelta = abs(dblThresholdTrigDelta);
  
  // Process window setup
  size_t unprocessed = processingSize - thresholdSize;
  
  size_t thresholdStartIndex = 0;

  // Simplified main processing loop - no averaging case only
  size_t processed = 0;
  size_t readIdx = thresholdSize;

  while (processed < unprocessed) {
    TFloat previousValue = data[thresholdStartIndex];
    TFloat currentValue = data[readIdx];
    ++thresholdStartIndex;
    ++readIdx;
    ++processed;

    // Check for front detection
    TFloat delta = abs(currentValue - previousValue);
    if (delta >= searchedDelta) {
      // Simplified value crossing validation integrated with front type check
      bool validFrontAndCrossing = false;

      if (frontType > 0) {
        // Rising front: previousValue <= currentValue AND previousValue <= dblSearchedValue <= currentValue
        validFrontAndCrossing = (previousValue <= currentValue) && (previousValue <= dblSearchedValue && dblSearchedValue <= currentValue);
      } else if (frontType < 0) {
        // Falling front: previousValue >= currentValue AND previousValue >= dblSearchedValue >= currentValue
        validFrontAndCrossing = (previousValue >= currentValue) && (previousValue >= dblSearchedValue && dblSearchedValue >= currentValue);
      } else {
        // Bidirectional: accept either rising or falling pattern with value crossing
        const bool risingWithCrossing = (previousValue <= currentValue) && (previousValue <= dblSearchedValue && dblSearchedValue <= currentValue);
        const bool fallingWithCrossing = (previousValue >= currentValue) && (previousValue >= dblSearchedValue && dblSearchedValue >= currentValue);
        validFrontAndCrossing = risingWithCrossing || fallingWithCrossing;
      }

      if (validFrontAndCrossing) {
        // Find precise value location using linear interpolation
        TFloat precisePos = findPreciseValueLocation(data, processingSize, dblSearchedValue, frontType);

        if (precisePos != -1) { // Valid position found
          // Add processingStartPos to get absolute position
          TFloat absolutePos = precisePos + static_cast<TFloat>(processingStartPos);
          // Use interpolated position if found
          if (absolutePos > dblFromPosition && absolutePos <= dblEndPosition) {
            frontPos = absolutePos;
            frontFound = true;
            return true;
          }
        }
      }
    }
  }

  return true; // executed, but no front found
}

template <typename TFloat>
TFloat SyncByFrwd<TFloat>::findPreciseValueLocation(const TFloat* data, size_t dataSize, TFloat searchedValue, int frontType) const {
  if (dataSize < 2) return static_cast<TFloat>(-1); // Invalid data size

  // Optimize for performance: use separate loops for different front types
  if (frontType > 0) {
    // Rising front: val1 <= searchedValue <= val2
    for (size_t i = 0; i < dataSize - 1; ++i) {
      auto val1 = data[i];
      auto val2 = data[i + 1];

      if (val1 <= searchedValue && searchedValue <= val2) {
        auto fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<TFloat>(i) + fraction;
      }
    }
  } else if (frontType < 0) {
    // Falling front: val1 >= searchedValue >= val2
    for (size_t i = 0; i < dataSize - 1; ++i) {
      TFloat val1 = data[i];
      TFloat val2 = data[i + 1];

      if (val1 >= searchedValue && searchedValue >= val2) {
        TFloat fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<TFloat>(i) + fraction;
      }
    }
  } else {
    // Bidirectional: accept either rising or falling pattern
    for (size_t i = 0; i < dataSize - 1; ++i) {
      TFloat val1 = data[i];
      TFloat val2 = data[i + 1];

      bool risingCrossing = (val1 <= searchedValue && searchedValue <= val2);
      bool fallingCrossing = (val1 >= searchedValue && searchedValue >= val2);

      if (risingCrossing || fallingCrossing) {
        TFloat fraction = interpolatePosition(val1, val2, searchedValue);
        return static_cast<TFloat>(i) + fraction;
      }
    }
  }

  return static_cast<TFloat>(-1); // No valid crossing found
}

template <typename TFloat>
inline TFloat SyncByFrwd<TFloat>::interpolatePosition(TFloat val1, TFloat val2, TFloat searchedValue) const {
  // Improved division by zero handling
  if (searchedValue == val1) return static_cast<TFloat>(0); // Start of interval
  if (searchedValue == val2) return static_cast<TFloat>(1); // End of interval

  // Return fractional position (denominator cannot be zero due to above checks)
  return (searchedValue - val1) / (val2 - val1);
}

// Explicit template instantiations
template class SyncByFrwd<float>;
template class SyncByFrwd<double>;

} // namespace IQVideoProcessor::SignalProcessing
