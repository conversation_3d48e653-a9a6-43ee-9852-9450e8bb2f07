#include "../sync_by_frwd.h"
#include <vector>
#include <iostream>
#include <sstream>
#include <cmath>
#include <algorithm>
#include <random>
#include <chrono>

using namespace IQVideoProcessor::SignalProcessing;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static std::vector<float> makeRamp(size_t total, size_t startIdx, size_t endIdx, float startVal, float endVal) {
  std::vector<float> v(total, startVal);
  if (endIdx > startIdx) {
    float step = (endVal - startVal) / static_cast<float>(endIdx - startIdx);
    for (size_t i = startIdx; i <= endIdx && i < total; ++i) {
      v[i] = startVal + step * static_cast<float>(i - startIdx);
    }
    for (size_t i = endIdx + 1; i < total; ++i) v[i] = endVal;
  }
  return v;
}

static std::vector<float> makeMultiStep(size_t total, const std::vector<std::pair<size_t, float>>& steps) {
  std::vector<float> v(total, steps.empty() ? 0.0f : steps[0].second);
  for (const auto& step : steps) {
    for (size_t i = step.first; i < total; ++i) {
      v[i] = step.second;
    }
  }
  return v;
}

static void buildPadded(const std::vector<float>& effective, size_t leftPad, size_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}

// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, ""}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what()}); \
  } \
} while(0)

// Various chunk sizes tests (64-8192 samples)
void test_various_chunk_sizes() {
  for (size_t chunkSize : {64, 128, 256, 512, 1024, 2048, 4096, 8192}) {
    size_t stepPos = chunkSize / 2;
    auto data = makeStep(chunkSize, stepPos, -1.0f, 1.0f);
    
    SyncByFrwd<float> finder(data.data(), chunkSize);
    float pos = 0.0f; bool found = false;
    
    // Search for value 0.0 (crossing point)
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Chunk size " << chunkSize << " failed";
      throw std::runtime_error(oss.str());
    }
    
    // Verify position accuracy
    if (std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      std::ostringstream oss;
      oss << "Position inaccurate for chunk size " << chunkSize << ": expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  std::cout << "✓ various chunk sizes (64-8192 samples)" << std::endl;
}

// Different searched values and interpolation scenarios
void test_searched_values_interpolation() {
  const size_t total = 1000;
  const size_t stepPos = 400;
  auto data = makeStep(total, stepPos, -2.0f, 2.0f);
  
  // Test various searched values that require interpolation
  std::vector<float> searchedValues = {-1.5f, -1.0f, -0.5f, 0.0f, 0.5f, 1.0f, 1.5f};
  
  for (float searchedValue : searchedValues) {
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, searchedValue, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Searched value " << searchedValue << " failed";
      throw std::runtime_error(oss.str());
    }
    
    // For rising front from -2 to 2, all these values should be found near stepPos
    if (std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      std::ostringstream oss;
      oss << "Position inaccurate for searched value " << searchedValue << ": expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ searched values and interpolation" << std::endl;
}

// Multiple threshold sizes and trigger delta combinations
void test_threshold_delta_combinations() {
  const size_t total = 800;
  const size_t stepPos = 300;
  auto data = makeStep(total, stepPos, -1.5f, 1.5f);
  
  std::vector<float> thresholdSizes = {2.0f, 5.0f, 10.0f, 20.0f, 50.0f};
  std::vector<float> triggerDeltas = {0.1f, 0.5f, 1.0f, 2.0f};
  
  for (float thresholdSize : thresholdSizes) {
    for (float triggerDelta : triggerDeltas) {
      SyncByFrwd<float> finder(data.data(), total);
      float pos = 0.0f; bool found = false;
      
      bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, thresholdSize, triggerDelta, pos, found);
      
      if (!ok) {
        std::ostringstream oss;
        oss << "Threshold " << thresholdSize << ", delta " << triggerDelta << " execution failed";
        throw std::runtime_error(oss.str());
      }
      
      // Should find front if delta is reasonable
      if (triggerDelta < 2.5f && !found) {
        std::ostringstream oss;
        oss << "Should have found front with threshold " << thresholdSize << ", delta " << triggerDelta;
        throw std::runtime_error(oss.str());
      }
      
      // If found, position should be reasonable
      if (found && std::abs(pos - static_cast<float>(stepPos)) > thresholdSize + 20.0f) {
        std::ostringstream oss;
        oss << "Position inaccurate for threshold " << thresholdSize << ", delta " << triggerDelta 
            << ": expected ~" << stepPos << ", got " << pos;
        throw std::runtime_error(oss.str());
      }
    }
  }
  
  std::cout << "✓ threshold and delta combinations" << std::endl;
}

// Edge cases: boundary crossings
void test_boundary_crossings() {
  // Crossing at very beginning
  {
    auto data = makeStep(500, 2, -1.0f, 1.0f);
    SyncByFrwd<float> finder(data.data(), 500);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.8f, pos, found);
    if (!ok || !found || pos > 10.0f) {
      throw std::runtime_error("Boundary crossing at beginning failed");
    }
  }
  
  // Crossing near end
  {
    auto data = makeStep(500, 480, -1.0f, 1.0f);
    SyncByFrwd<float> finder(data.data(), 500);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.8f, pos, found);
    if (!ok || !found || std::abs(pos - 480.0f) > 10.0f) {
      throw std::runtime_error("Boundary crossing near end failed");
    }
  }
  
  // Crossing exactly at threshold boundary
  {
    auto data = makeStep(200, 50, -1.0f, 1.0f);
    SyncByFrwd<float> finder(data.data(), 200);
    float pos = 0.0f; bool found = false;
    
    // Use threshold size that aligns with crossing
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 50.0f, 0.8f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Threshold boundary crossing failed");
    }
  }
  
  std::cout << "✓ boundary crossings" << std::endl;
}

// Multiple value occurrences
void test_multiple_value_occurrences() {
  // Create signal with multiple crossings of the same value
  std::vector<float> data(1000, 0.0f);
  
  // Multiple step transitions
  for (size_t i = 100; i < 200; ++i) data[i] = 2.0f;  // First crossing at 100
  for (size_t i = 300; i < 400; ++i) data[i] = 2.0f;  // Second crossing at 300
  for (size_t i = 500; i < 600; ++i) data[i] = 2.0f;  // Third crossing at 500
  
  SyncByFrwd<float> finder(data.data(), 1000);
  float pos = 0.0f; bool found = false;
  
  // Search for value 1.0 (should find first crossing)
  bool ok = finder(true, +1, 1.0f, 0.0f, 0.0f, 5.0f, 0.8f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Multiple value occurrences test failed");
  }
  
  // Should find the first occurrence
  if (pos > 150.0f) {
    std::ostringstream oss;
    oss << "Should find first occurrence, got position " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ multiple value occurrences" << std::endl;
}

// High-volume performance test
void test_high_volume_performance() {
  const size_t totalSamples = 20000000; // 20M samples
  const size_t stepPos = totalSamples / 2;
  
  // Create large dataset
  auto data = makeStep(totalSamples, stepPos, -1.0f, 1.0f);
  
  SyncByFrwd<float> finder(data.data(), totalSamples);
  float pos = 0.0f; bool found = false;
  
  auto start = std::chrono::high_resolution_clock::now();
  bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 8192.0f, 0.5f, pos, found);
  auto end = std::chrono::high_resolution_clock::now();
  
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  
  if (!ok || !found) {
    throw std::runtime_error("High volume performance test failed");
  }
  
  // Verify position accuracy
  if (std::abs(pos - static_cast<float>(stepPos)) > 10000.0f) {
    std::ostringstream oss;
    oss << "High volume position inaccurate: expected ~" << stepPos << ", got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  // Performance check - should complete in reasonable time (less than 5 seconds)
  if (duration.count() > 5000) {
    std::ostringstream oss;
    oss << "High volume test too slow: " << duration.count() << "ms";
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ high volume performance (20M samples, " << duration.count() << "ms)" << std::endl;
}

// Value-based synchronization accuracy with linear interpolation validation
void test_interpolation_accuracy() {
  // Create precise test data for interpolation validation
  std::vector<float> data = {-2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f, 4.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  
  // Test interpolation between known values
  struct TestCase {
    float searchedValue;
    float expectedPos;
    std::string description;
  };
  
  std::vector<TestCase> testCases = {
    {-1.5f, 0.5f, "halfway between -2 and -1"},
    {-0.5f, 1.5f, "halfway between -1 and 0"},
    {0.5f, 2.5f, "halfway between 0 and 1"},
    {1.5f, 3.5f, "halfway between 1 and 2"},
    {2.5f, 4.5f, "halfway between 2 and 3"}
  };
  
  for (const auto& testCase : testCases) {
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, testCase.searchedValue, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Interpolation test failed for " << testCase.description;
      throw std::runtime_error(oss.str());
    }
    
    // Verify interpolation accuracy
    if (std::abs(pos - testCase.expectedPos) > 0.1f) {
      std::ostringstream oss;
      oss << "Interpolation inaccurate for " << testCase.description 
          << ": expected " << testCase.expectedPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ interpolation accuracy validation" << std::endl;
}

// Overlap ratios and chunk processing patterns
void test_overlap_ratios() {
  const size_t baseChunkSize = 1024;
  const size_t stepPos = 500;
  auto data = makeStep(baseChunkSize * 3, stepPos, -1.0f, 1.0f);

  // Test different overlap ratios
  std::vector<float> overlapRatios = {0.1f, 0.25f, 0.5f, 0.75f};

  for (float overlapRatio : overlapRatios) {
    size_t overlapSize = static_cast<size_t>(baseChunkSize * overlapRatio);

    // Process first chunk
    SyncByFrwd<float> finder1(data.data(), baseChunkSize + overlapSize);
    float pos1 = 0.0f; bool found1 = false;

    bool ok1 = finder1(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f, pos1, found1);

    // Process second chunk with overlap
    size_t secondChunkStart = baseChunkSize - overlapSize;
    SyncByFrwd<float> finder2(data.data() + secondChunkStart, baseChunkSize + overlapSize);
    float pos2 = 0.0f; bool found2 = false;

    bool ok2 = finder2(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.5f, pos2, found2);

    if (!ok1 || !ok2) {
      std::ostringstream oss;
      oss << "Overlap ratio " << overlapRatio << " execution failed";
      throw std::runtime_error(oss.str());
    }

    // If both found, verify consistency
    if (found1 && found2) {
      float adjustedPos2 = pos2 + static_cast<float>(secondChunkStart);
      if (std::abs(pos1 - adjustedPos2) > overlapSize + 10.0f) {
        std::ostringstream oss;
        oss << "Overlap consistency failed for ratio " << overlapRatio
            << ": pos1=" << pos1 << ", adjusted_pos2=" << adjustedPos2;
        throw std::runtime_error(oss.str());
      }
    }
  }

  std::cout << "✓ overlap ratios and chunk processing" << std::endl;
}

// Front type specificity tests
void test_front_type_specificity() {
  const size_t total = 600;

  // Rising front test
  {
    auto data = makeStep(total, 200, -2.0f, 2.0f);
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    // Should find with rising front type
    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Rising front specificity test failed");
    }

    // Should NOT find with falling front type
    bool ok2 = finder(true, -1, 0.0f, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok2 || found) {
      throw std::runtime_error("Rising front should not be detected as falling");
    }
  }

  // Falling front test
  {
    auto data = makeStep(total, 200, 2.0f, -2.0f);
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    // Should find with falling front type
    bool ok = finder(true, -1, 0.0f, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Falling front specificity test failed");
    }

    // Should NOT find with rising front type
    bool ok2 = finder(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok2 || found) {
      throw std::runtime_error("Falling front should not be detected as rising");
    }
  }

  // Bidirectional should find both
  {
    auto data = makeStep(total, 200, -2.0f, 2.0f);
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, 0, 0.0f, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Bidirectional front test failed");
    }
  }

  std::cout << "✓ front type specificity" << std::endl;
}

// Precision edge cases
void test_precision_edge_cases() {
  // Very small value differences
  {
    std::vector<float> data(200, 1.0f);
    data[100] = 1.0f + 1e-5f;

    SyncByFrwd<float> finder(data.data(), 200);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 1.0f + 5e-6f, 0.0f, 0.0f, 5.0f, 5e-6f, pos, found);
    if (!ok) {
      throw std::runtime_error("Precision edge case execution failed");
    }
    // May or may not find - just ensure it doesn't crash
  }

  // Very large values
  {
    std::vector<float> data(200, 1e6f);
    data[100] = 1e6f + 1000.0f;

    SyncByFrwd<float> finder(data.data(), 200);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 1e6f + 500.0f, 0.0f, 0.0f, 5.0f, 500.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Large values precision test failed");
    }
  }

  std::cout << "✓ precision edge cases" << std::endl;
}

// Stress test with unusual parameter combinations
void test_unusual_parameter_combinations() {
  const size_t total = 10000;
  const size_t stepPos = 5000;
  auto data = makeStep(total, stepPos, -3.0f, 3.0f);

  // Very large threshold size
  {
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 1000.0f, 1.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Large threshold stress test failed");
    }
  }

  // Very small threshold with large delta
  {
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 1.0f, 5.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Small threshold large delta test failed");
    }
  }

  // Very small delta with large threshold
  {
    SyncByFrwd<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 0.0f, 100.0f, 0.1f, pos, found);
    if (!ok) {
      throw std::runtime_error("Large threshold small delta test execution failed");
    }
    // May or may not find - just ensure it doesn't crash
  }

  std::cout << "✓ unusual parameter combinations" << std::endl;
}

// Direct data comparison for consecutive chunks
void test_consecutive_chunk_comparison() {
  const size_t chunkSize = 2048;
  const size_t overlapSize = 256;
  const size_t totalSize = chunkSize * 3;
  const size_t stepPos = chunkSize + 100; // Step in second chunk

  auto data = makeStep(totalSize, stepPos, -1.5f, 1.5f);

  // Process first chunk
  SyncByFrwd<float> finder1(data.data(), chunkSize);
  float pos1 = 0.0f; bool found1 = false;
  bool ok1 = finder1(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.8f, pos1, found1);

  // Process second chunk with overlap
  size_t chunk2Start = chunkSize - overlapSize;
  SyncByFrwd<float> finder2(data.data() + chunk2Start, chunkSize);
  float pos2 = 0.0f; bool found2 = false;
  bool ok2 = finder2(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.8f, pos2, found2);

  // Process third chunk with overlap
  size_t chunk3Start = chunkSize * 2 - overlapSize;
  SyncByFrwd<float> finder3(data.data() + chunk3Start, chunkSize);
  float pos3 = 0.0f; bool found3 = false;
  bool ok3 = finder3(true, +1, 0.0f, 0.0f, 0.0f, 5.0f, 0.8f, pos3, found3);

  if (!ok1 || !ok2 || !ok3) {
    throw std::runtime_error("Consecutive chunk processing failed");
  }

  // First chunk should not find the step
  if (found1) {
    throw std::runtime_error("First chunk should not find step");
  }

  // Second chunk should find the step
  if (!found2) {
    throw std::runtime_error("Second chunk should find step");
  }

  // Third chunk should not find the step (already passed)
  if (found3) {
    throw std::runtime_error("Third chunk should not find step");
  }

  // Verify position accuracy in second chunk
  float expectedPosInChunk2 = static_cast<float>(stepPos - chunk2Start);
  if (std::abs(pos2 - expectedPosInChunk2) > 10.0f) {
    std::ostringstream oss;
    oss << "Position in second chunk inaccurate: expected ~" << expectedPosInChunk2 << ", got " << pos2;
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ consecutive chunk comparison" << std::endl;
}

// Regression test for precise value location algorithm
void test_precise_value_location_regression() {
  // This test specifically targets the bug that was fixed in findPreciseValueLocation
  std::vector<float> data = {0.0f, 0.0f, 0.0f, 0.5f, 1.0f, 1.5f, 2.0f, 2.0f, 2.0f};

  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;

  // Search for value 0.75 which should be between indices 3 and 4 (0.5 and 1.0)
  bool ok = finder(true, +1, 0.75f, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);

  if (!ok || !found) {
    throw std::runtime_error("Precise value location regression test failed");
  }

  // Expected position should be 3.5 (halfway between indices 3 and 4)
  if (std::abs(pos - 3.5f) > 0.1f) {
    std::ostringstream oss;
    oss << "Precise value location incorrect: expected ~3.5, got " << pos;
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ precise value location regression" << std::endl;
}

int main() {
  std::cout << "SyncByFrwd Comprehensive Test Suite" << std::endl;
  std::cout << "===================================" << std::endl;

  // Run all comprehensive tests
  RUN_TEST(test_various_chunk_sizes);
  RUN_TEST(test_searched_values_interpolation);
  RUN_TEST(test_threshold_delta_combinations);
  RUN_TEST(test_boundary_crossings);
  RUN_TEST(test_multiple_value_occurrences);
  RUN_TEST(test_high_volume_performance);
  RUN_TEST(test_interpolation_accuracy);
  RUN_TEST(test_overlap_ratios);
  RUN_TEST(test_front_type_specificity);
  RUN_TEST(test_precision_edge_cases);
  RUN_TEST(test_unusual_parameter_combinations);
  RUN_TEST(test_consecutive_chunk_comparison);
  RUN_TEST(test_precise_value_location_regression);

  // Print results summary
  std::cout << std::endl;
  std::cout << "===================================================" << std::endl;
  std::cout << "COMPREHENSIVE SYNC TEST RESULTS" << std::endl;
  std::cout << "===================================================" << std::endl;

  int passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << std::endl;
  std::cout << "Total: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;

  return failed > 0 ? 1 : 0;
}
