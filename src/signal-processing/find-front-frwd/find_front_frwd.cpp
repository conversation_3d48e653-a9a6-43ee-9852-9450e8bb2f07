#include "find_front_frwd.h"
#include <cassert>
#include <cmath>

namespace IQVideoProcessor::SignalProcessing {

// Note: keep architecture/API. thresholdRing_ is an external mutable dependency
// intentionally updated during operator() despite this method being const.

using std::abs;
using std::ceil;

template <typename TFloat>
FindFrontFrwd<TFloat>::FindFrontFrwd(const TFloat* data, std::size_t effectiveLen, std::vector<TFloat>& thresholdRing)
  : data_(data), effectiveLen_(effectiveLen), thresholdRing_(thresholdRing) {
  // Allow empty buffers: data_ may be null when effectiveLen_ == 0
  assert((effectiveLen_ == 0) || (data_ != nullptr));
}

template <typename TFloat>
bool FindFrontFrwd<TFloat>::operator()(
  bool toEOF,
  int frontType,
  TFloat dblFromPosition,
  TFloat dblElements,
  TFloat dblAveSize,
  TFloat dblThresholdSize,
  TFloat dblThresholdTrigDelta,
  TFloat& frontPos,
  bool& frontFound) const
{
  frontFound = false;

  // Derive integer parameters (original rounding and odd policy)
  size_t aveSize = dblAveSize < static_cast<TFloat>(1) ? 1 : static_cast<size_t>(dblAveSize + static_cast<TFloat>(0.5));
  const size_t halfAveSize = aveSize >> 1;
  aveSize = (halfAveSize << 1) + 1; // force to odd: even -> +1, odd unchanged

  const size_t thresholdSize = dblThresholdSize <= static_cast<TFloat>(1) ? 1 : static_cast<size_t>(dblThresholdSize + static_cast<TFloat>(0.5));
  if (thresholdSize > thresholdRing_.size()) return false; // Error: threshold size exceeds the capacity of the external ring

  TFloat dblEndPosition = dblFromPosition + dblElements;
  const size_t processingStartPos = static_cast<size_t>(dblFromPosition);

  size_t processingSize;
  if (toEOF) {
    processingSize = (processingStartPos < effectiveLen_) ? (effectiveLen_ - processingStartPos) : 0u;
    dblEndPosition = static_cast<TFloat>(effectiveLen_ - 1); // to EOF means to the last element
  } else if (dblElements <= static_cast<TFloat>(0)) {
    processingSize = 0u;
  } else {
    processingSize = static_cast<size_t>(ceil(dblEndPosition)) - processingStartPos;
    if (processingStartPos + processingSize > effectiveLen_) processingSize = effectiveLen_ - processingStartPos;
  }
  if (processingSize == 0 || processingSize <= thresholdSize) return true; // nothing to process, but not an error

  const TFloat* const data = data_ + processingStartPos; // base pointer for processing

  // Initialize threshold ring with either ave sums or raw values
  if (aveSize > 1) {
    const TFloat *dataWithHalfAveOffset = data - halfAveSize; // shifting left by halfAveSize
    auto summ = static_cast<TFloat>(0);
    for (size_t i = 0; i < aveSize; ++i) {
      summ += dataWithHalfAveOffset[i];
    }
    thresholdRing_[0] = summ;
    //
    for (size_t k = 1; k < thresholdSize; ++k) {
      auto readIndex = k - 1;
      summ -= dataWithHalfAveOffset[readIndex];
      summ += dataWithHalfAveOffset[readIndex + aveSize];
      thresholdRing_[k] = summ;
    }
  }

  // Scale threshold for sum-domain when aveSize>1
  TFloat searchedDelta = abs(dblThresholdTrigDelta);
  // Process window
  size_t totalProcessed = thresholdSize - 1;
  size_t unprocessed = processingSize - thresholdSize;

  size_t thresholdStartIndex = 0;
  size_t thresholdEndIndex = thresholdSize - 1;
  TFloat dblThresholdHalfSize = static_cast<TFloat>(thresholdSize) / static_cast<TFloat>(2); // for center position calculation

  // Main processing loop
  if (aveSize > 1) {
    searchedDelta *= static_cast<TFloat>(aveSize);

    size_t processed = 0;
    size_t readIdx = thresholdSize; // Start reading after threshold initialization

    while (processed < unprocessed) {
      // Get values from circular buffer
      TFloat currentValue = thresholdRing_[thresholdEndIndex];
      TFloat previousValue = thresholdRing_[thresholdStartIndex];
      // Update the moving sum for next position
      currentValue -= data[readIdx - aveSize]; // Remove the leftmost element from the current sum
      currentValue += data[readIdx]; // Add the rightmost element to create new sum
      // Store updated sum and advance indices
      thresholdRing_[thresholdStartIndex] = currentValue;
      ++thresholdEndIndex;
      ++thresholdStartIndex;
      ++readIdx;
      ++processed;

      if (thresholdStartIndex == thresholdSize) thresholdStartIndex = 0;
      if (thresholdEndIndex   == thresholdSize) thresholdEndIndex   = 0;

      // Check for front detection
      TFloat delta = abs(currentValue - previousValue);
      if (delta >= searchedDelta) {
        if ((frontType >= 0 && previousValue <= currentValue) || (frontType <= 0 && previousValue >= currentValue)) {
          // Calculate front position
          TFloat calculatedPos = static_cast<TFloat>(processingStartPos + totalProcessed + processed) - dblThresholdHalfSize;
          if (calculatedPos > dblFromPosition && calculatedPos <= dblEndPosition) {
            frontPos = calculatedPos;
            frontFound = true;
            return true;
          }
        }
      }
    }
  } else {
    // aveSize == 1 case
    size_t processed = 0;
    size_t readIdx = thresholdSize;

    while (processed < unprocessed) {
      TFloat previousValue = data[thresholdStartIndex];
      TFloat currentValue = data[readIdx];
      ++thresholdStartIndex;
      ++readIdx;
      ++processed;

      // Check for front
      TFloat delta = abs(currentValue - previousValue);
      if (delta >= searchedDelta) {
        if ((frontType >= 0 && previousValue <= currentValue) || (frontType <= 0 && previousValue >= currentValue)) {
          // Calculate front position
          TFloat calculatedPos = static_cast<TFloat>(processingStartPos + totalProcessed + processed) - dblThresholdHalfSize;
          if (calculatedPos > dblFromPosition && calculatedPos <= dblEndPosition) {
            frontPos = calculatedPos;
            frontFound = true;
            return true;
          }
        }
      }
    }
  }

  return true; // executed, but no front found
}

// Explicit template instantiations

template class FindFrontFrwd<float>;
template class FindFrontFrwd<double>;

} // namespace IQVideoProcessor::SignalProcessing


