#if !defined(_SYNC_BY_INCLUDED_)
#define _SYNC_BY_INCLUDED_

#include "../processor/processor.h"

namespace Processing
{

class CSynchByFrwdProcessor : private CProcessor
{
public:
    CSynchByFrwdProcessor(CUniversalSource& source);
    bool operator()(bool toEOF, int frontType, double dblSearchedValue, double dblFromPosition, double dblElements, double dblAveSize, double dblThresholdSize, double dblThresholdTrigDelta, double& synchPos, bool& synchFound);    
};

class CSynchByBkwdProcessor : private CProcessor
{
public:
    CSynchByBkwdProcessor(CUniversalSource& source);
    bool operator()(bool toSOF, int frontType, double dblSearchedValue, double dblFromPosition, double dblElements, double dblAveSize, double dblThresholdSize, double dblThresholdTrigDelta, double& synchPos, bool& synchFound);    
};

} // namespace Processing

#endif