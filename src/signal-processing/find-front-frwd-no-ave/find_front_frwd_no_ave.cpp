#include "find_front_frwd_no_ave.h"
#include <cassert>
#include <cmath>

namespace IQVideoProcessor::SignalProcessing {

using std::abs;
using std::ceil;

template <typename TFloat>
FindFrontFrwdNoAve<TFloat>::FindFrontFrwdNoAve(const TFloat* data, std::size_t effectiveLen)
  : data_(data), effectiveLen_(effectiveLen) {
  // Allow empty buffers: data_ may be null when effectiveLen_ == 0
  assert((effectiveLen_ == 0) || (data_ != nullptr));
}

template <typename TFloat>
bool FindFrontFrwdNoAve<TFloat>::operator()(
  bool toEOF,
  int frontType,
  TFloat dblFromPosition,
  TFloat dblElements,
  TFloat dblThresholdSize,
  TFloat dblThresholdTrigDelta,
  TFloat& frontPos,
  bool& frontFound) const
{
  frontFound = false;

  // Simplified parameter derivation - no averaging logic needed
  // aveSize is always 1, so no odd forcing or halfAveSize calculation
  const size_t thresholdSize = dblThresholdSize <= static_cast<TFloat>(1) ? 1 : static_cast<size_t>(dblThresholdSize + static_cast<TFloat>(0.5));

  TFloat dblEndPosition = dblFromPosition + dblElements;
  const size_t processingStartPos = static_cast<size_t>(dblFromPosition);

  size_t processingSize;
  if (toEOF) {
    processingSize = (processingStartPos < effectiveLen_) ? (effectiveLen_ - processingStartPos) : 0u;
    dblEndPosition = static_cast<TFloat>(effectiveLen_ - 1); // to EOF means to the last element
  } else if (dblElements <= static_cast<TFloat>(0)) {
    processingSize = 0u;
  } else {
    processingSize = static_cast<size_t>(ceil(dblEndPosition)) - processingStartPos;
    if (processingStartPos + processingSize > effectiveLen_) processingSize = effectiveLen_ - processingStartPos;
  }
  if (processingSize == 0 || processingSize <= thresholdSize) return true; // nothing to process, but not an error

  const TFloat* const data = data_ + processingStartPos; // base pointer for processing

  // No ring buffer initialization needed - we read directly from data
  
  // Threshold is used as-is (no scaling for averaging since aveSize=1)
  TFloat searchedDelta = abs(dblThresholdTrigDelta);
  
  // Process window setup
  size_t totalProcessed = thresholdSize - 1;
  size_t unprocessed = processingSize - thresholdSize;
  
  size_t thresholdStartIndex = 0;
  TFloat dblThresholdHalfSize = static_cast<TFloat>(thresholdSize) / static_cast<TFloat>(2); // for center position calculation

  // Simplified main processing loop - no averaging case only
  size_t processed = 0;
  size_t readIdx = thresholdSize;

  while (processed < unprocessed) {
    TFloat previousValue = data[thresholdStartIndex];
    TFloat currentValue = data[readIdx];

    // Check for front detection
    TFloat delta = abs(currentValue - previousValue);
    if (delta >= searchedDelta) {
      if ((frontType >= 0 && previousValue <= currentValue) || (frontType <= 0 && previousValue >= currentValue)) {
        // Calculate front position
        TFloat calculatedPos = static_cast<TFloat>(processingStartPos + processed) + dblThresholdHalfSize;
        if (calculatedPos > dblFromPosition && calculatedPos <= dblEndPosition) {
          frontPos = calculatedPos;
          frontFound = true;
          return true;
        }
      }
    }

    ++thresholdStartIndex;
    ++readIdx;
    ++processed;
  }

  return true; // executed, but no front found
}

// Explicit template instantiations
template class FindFrontFrwdNoAve<float>;
template class FindFrontFrwdNoAve<double>;

} // namespace IQVideoProcessor::SignalProcessing
