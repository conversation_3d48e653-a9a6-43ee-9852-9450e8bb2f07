#pragma once

#include <vector>
#include "../../types.h"

namespace IQVideoProcessor::SignalProcessing {

template <typename TFloat = float>
class FindFrontFrwdNoAve {
public:
  explicit FindFrontFrwdNoAve(const TFloat* data, std::size_t effectiveLen);

  bool operator()(bool toEOF,
                  int frontType,
                  TFloat dblFromPosition,
                  TFloat dblElements,
                  TFloat dblThresholdSize,
                  TFloat dblThresholdTrigDelta,
                  TFloat& frontPos,
                  bool& frontFound) const;

private:
  const TFloat* data_{nullptr};
  std::size_t effectiveLen_{0};
  // Note: No thresholdRing_ member - not needed for non-averaging algorithm
};

} // namespace IQVideoProcessor::SignalProcessing
