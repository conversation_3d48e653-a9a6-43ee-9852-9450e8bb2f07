#include "./line_detection_node.h"
#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

LineDetectionNode::LineDetectionNode(SampleRateType sampleRate, size_t maxVideoLineSamples)
  : sampleRate_(sampleRate),
    maxVideoLineSamples_(maxVideoLineSamples)
  {
  _ds500kHz_.totalSamples = 0;
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto effectiveStartIdx = segment.leftOverlapSamples;
  auto effectiveSamples = segment.effectiveSamples;

  auto& filteredSegment = getAveFiltered500kHzSegment(segment);
  auto [minSampleValue, maxSampleValue] = getMinMax(&filteredSegment.data[effectiveStartIdx], effectiveSamples);
  auto signalScale = maxSampleValue - minSampleValue;

  return running();
}

const AveFilteredDemodulatedSegment& LineDetectionNode::getAveFiltered500kHzSegment(const DemodulatedSegment & segment) {
  if (_ds500kHz_.totalSamples == 0) {
    // Initialize the 500kHz segment with the first segment data
    _ds500kHz_.data.resize(segment.totalSamples);
    _ds500kHz_.totalSamples = segment.totalSamples;
    _ds500kHz_.effectiveSamples = segment.effectiveSamples;
    _ds500kHz_.leftOverlapSamples = segment.leftOverlapSamples;
    _ds500kHz_.segmentIndex = segment.segmentIndex;
    // Calculating the filter divider based on the sample rate
    _ds500kHz_.halfAveSize = (sampleRate_ / 500000) >> 1; // 500kHz
    _ds500kHz_.aveSize = (_ds500kHz_.halfAveSize << 1) + 1; // Force to odd: even -> +1, odd unchanged
  }

  const auto aveSize = _ds500kHz_.aveSize;
  const auto halfAveSize = _ds500kHz_.halfAveSize;

  ComplexType summ = 0;
  for (auto i = 0; i < aveSize; ++i) {
    summ += segment.data[i];
  }
  // Fill the start of the segment with the initial sum
  for (auto i = 0; i <= halfAveSize; ++i) {
    _ds500kHz_.data[i] = summ; // Fill the first half with the initial sum
  }

  auto summLeaveIdx = 0;
  auto summEnterIdx = aveSize;
  auto summWriteIdx = halfAveSize + 1;

  // Sliding window to calculate the sum for the rest of the segment
  while (summEnterIdx < segment.totalSamples) {
    summ -= segment.data[summLeaveIdx++];
    summ += segment.data[summEnterIdx++];
    _ds500kHz_.data[summWriteIdx++] = summ;
  }
  // Fill the rest of the segment with the last sum
  while (summWriteIdx < segment.totalSamples) {
    _ds500kHz_.data[summWriteIdx++] = summ;
  }

  return _ds500kHz_;
}

std::tuple<ComplexType, ComplexType> LineDetectionNode::getMinMax(const ComplexType* data, const size_t elements) {
  ComplexType minVal = data[0];
  ComplexType maxVal = data[0];
  for (size_t i = 1; i < elements; ++i) {
    if (data[i] < minVal) minVal = data[i];
    if (data[i] > maxVal) maxVal = data[i];
  }
  return std::make_tuple(minVal, maxVal);
}

} // namespace IQVideoProcessor::Pipeline
