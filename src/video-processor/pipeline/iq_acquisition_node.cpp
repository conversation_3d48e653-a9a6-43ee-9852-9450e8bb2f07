#include "./iq_acquisition_node.h"
#include "../video_processor_configs.h"
#include <cassert>

namespace IQVideoProcessor::Pipeline {

IQAcquisitionNode::IQAcquisitionNode(
  std::unique_ptr<IIQStream> stream,
  size_t iqStreamReadSize,
  size_t effectiveSamplesPerSegment,
  size_t leftOverlapSamples
) :
  iqStreamReadSize_(iqStreamReadSize),
  effectiveSamplesPerSegment_(effectiveSamplesPerSegment),
  leftOverlapSamples_(leftOverlapSamples),
  iqStream_(std::move(stream)),
  nextSegmentIndex_(0)
{

  if (!iqStream_) return; // just do nothing if stream is null

  assert(iqStreamReadSize_ > 0);
  assert(effectiveSamplesPerSegment_ > 0);
  assert(leftOverlapSamples_ > 0);

  const auto chunksOverlap = leftOverlapSamples_ * 2; // Overlap must be 2x the left overlap (left+right)
  const auto chunkSize = effectiveSamplesPerSegment_ + chunksOverlap; // left overlap + effective region + right overlap
  const auto approxBufferSize = chunkSize * 4; // Heuristic: ~4x window size

  auto numOfBuffers = (approxBufferSize / iqStreamReadSize_) + 3; // Add 3 extra chunks for safety
  numOfBuffers = std::max(numOfBuffers, static_cast<size_t>(IQ_STREAM_MIN_READ_BUFF_NUM)); // Ensure we have at least 16 write chunks

  // Initialize the reusable window buffer with sufficient capacity and defaults
  currentSegment_.data.resize(chunkSize);
  currentSegment_.totalSamples = chunkSize;
  currentSegment_.effectiveSamples = effectiveSamplesPerSegment_;
  currentSegment_.leftOverlapSamples = leftOverlapSamples_;

  /*
  * Creating a chunk processor that generates chunks with the pre-calculated sizes
  * passed from VideoProcessor.
  */
  chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
    iqStreamReadSize_,
    chunkSize,
    chunksOverlap,
    numOfBuffers,
    ([this](const SampleType *chunkBuffer, const size_t chunkSize, const size_t chunkOverlap) {
      (void)chunkOverlap;
      this->onOutputChunkReady(chunkBuffer, chunkSize);
    })
  );
  // Making the node running
  setRunning();
}

IQAcquisitionNode::~IQAcquisitionNode() {
  PipelineComponent::stop();
}

bool IQAcquisitionNode::process(bool &) {
  return false; // We don't process input directly in this node
}

bool IQAcquisitionNode::hasPendingWork() const {
  return false;
}

bool IQAcquisitionNode::tick() {
  if (!running()) {
    return false; // Node is not running
  }

  auto *buffer = chunkProcessor_->getWriteChunkPtr();
  if (!iqStream_->readSamples(buffer, iqStreamReadSize_)) {
    stop();
    return false;
  }

  if (!running()) {
    return false; // Node is not running
  }

  chunkProcessor_->commitWriteChunk();
  return true;
}

void IQAcquisitionNode::onOutputChunkReady(const SampleType *chunkBuffer, const size_t chunkSize) {
  if (!running()) return;

  constexpr ComplexType kScale = 1.0f / 32768.0f; // Normalization scale for int16_t range

  // Convert packed 0xQQQQIIII into normalized complex values
  for (size_t i = 0; i < chunkSize; ++i) {
    const uint32_t word = chunkBuffer[i];  // make sure this is unsigned
    const auto i_raw = static_cast<int16_t>(word);         // low 16 bits
    const auto q_raw = static_cast<int16_t>(word >> 16);   // high 16 bits
    currentSegment_.data[i].real(static_cast<ComplexType>(i_raw) * kScale);
    currentSegment_.data[i].imag(static_cast<ComplexType>(q_raw) * kScale);
  }

  currentSegment_.segmentIndex = nextSegmentIndex_++;

  if (!this->sendOutput(currentSegment_)) {
    // If output link rejected the data, we should stop processing
    stop();
  }
}

} // namespace IQVideoProcessor::Pipeline

