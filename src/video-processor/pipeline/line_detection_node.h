#pragma once

#include "./iq_demodulation_node_types.h"
#include "../../stream-pipeline/stream_node.h"
#include "../../types.h"
#include <tuple>

namespace IQVideoProcessor::Pipeline {

struct AveFilteredDemodulatedSegment: DemodulatedSegment {
  size_t aveSize = 0;
  size_t halfAveSize = 0;
};

class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment, DemodulatedSegment> {
public:
  LineDetectionNode(SampleRateType sampleRate, size_t maxVideoLineSamples);
  ~LineDetectionNode() override;

private:
  bool process(DemodulatedSegment& segment) override;

  SampleRateType sampleRate_;
  size_t maxVideoLineSamples_;

  const AveFilteredDemodulatedSegment& getAveFiltered500kHzSegment(const DemodulatedSegment & segment);
  static std::tuple<ComplexType, ComplexType> getMinMax(const ComplexType* data, size_t elements);

  // Internal store
  AveFilteredDemodulatedSegment _ds500kHz_;
};

} // namespace IQVideoProcessor::Pipeline
