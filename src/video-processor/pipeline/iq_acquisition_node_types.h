#pragma once

#include <vector>
#include <complex>
#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

// Holds a segment of IQ samples with overlap guards around an effective region.
struct ComplexIQSegment {
  std::vector<std::complex<ComplexType>> data;
  size_t totalSamples;
  size_t effectiveSamples;
  size_t leftOverlapSamples;
  size_t segmentIndex;
};

} // namespace IQVideoProcessor::Pipeline
